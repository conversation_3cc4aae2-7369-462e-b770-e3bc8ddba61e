import { State, createState } from '../../../api/core/script/state'
import { createGeState } from '../../../api/script-utils/states/geStates'
import { Withdraw } from '../../../api/game/bank'
import { GeAction } from '../../../api/game/geAction'
import { ItemId } from '../../../data/itemId'
import { Equipment } from '../../../api/game/equipment'
import { Inventory } from '../../../api/game/inventory'
import { Bank } from '../../../api/game/bank'
import { Time } from '../../../api/utils/time'
import { log } from '../../../api/utils/utils'
import { Widgets } from '../../../api/game/widgets'

export type BindTiaraType = {
    name: string
    varbitId: number
    itemId: number
}

export const BindTiaraTypes = {
    WATER_TIARA: {
        name: 'WATER_TIARA',
        varbitId: 0, // TODO: Find actual varbit ID for water tiara binding completion
        itemId: ItemId.WATER_TIARA
    },
    EARTH_TIARA: {
        name: 'EARTH_TIARA',
        varbitId: 0, // TODO: Find actual varbit ID for earth tiara binding completion
        itemId: ItemId.EARTH_TIARA
    },
}

export class BindTiaraToHat extends State {
    defaultState: () => State
    resupplyState: () => State
    tiaraType: BindTiaraType

    constructor(tiaraType: BindTiaraType, defaultState: () => State, resupplyState: () => State) {
        super()
        this.name = `Binding ${tiaraType.name.toLowerCase()} to hat`
        this.tiaraType = tiaraType
        this.defaultState = defaultState
        this.resupplyState = resupplyState
    }

    onAction(): void {
        this.setState(this.prepareItems)
    }

    prepareItems = createState('Prepare Items (for binding)', () => {
        // Check if we have both items and binding is complete
        if (Inventory.contains(this.tiaraType.itemId) && Inventory.contains(ItemId.HAT_OF_THE_EYE)) {
            this.setState(this.bindTiara)
            return
        }

        // If HAT_OF_THE_EYE is equipped, unequip it first
        if (Equipment.isEquipped(ItemId.HAT_OF_THE_EYE)) {
            if (Inventory.getFreeSlots() < 1) {
                if (!Bank.openNearest() || !Bank.depositAll()) {
                    return
                }
            }
            Equipment.unequip(ItemId.HAT_OF_THE_EYE)
            return
        }

        if (!Bank.openNearest()) {
            return
        }

        // Withdraw the specified tiara
        if (!Withdraw.id(this.tiaraType.itemId, 1)
            .minimumAmount(1)
            .orState(this.geState)
            .ensureSpace()
            .withdraw()) {
            return
        }

        // Withdraw HAT_OF_THE_EYE
        if (!Withdraw.id(ItemId.HAT_OF_THE_EYE, 1)
            .minimumAmount(1)
            .orState(null)
            .ensureSpace()
            .withdraw()) {
            return
        }

        Widgets.closeTopInterface()

    })

    bindTiara = createState('Binding tiara to hat', () => {
        if (!Inventory.contains(ItemId.WATER_TIARA) || !Inventory.contains(ItemId.HAT_OF_THE_EYE)) {
            this.setState(this.prepareItems)
            return
        }

        // Use water tiara on HAT_OF_THE_EYE
        const waterTiara = Inventory.getById(ItemId.WATER_TIARA)
        if (waterTiara) {
            log("Using water tiara on hat of the eye")
            waterTiara.useOnItem(ItemId.HAT_OF_THE_EYE)
            
            // Wait for the action to complete
            if (Time.sleep(2000, 3000, () => !Inventory.contains(ItemId.WATER_TIARA) || !Inventory.contains(ItemId.HAT_OF_THE_EYE))) {
                this.setState(this.defaultState())
                return
            }
        }
    })

    geState = createGeState(() => this.prepareItems, () => this.resupplyState(), [
        GeAction.item(ItemId.WATER_TIARA, 1).gePrice(1.1, 5000).buy()
    ], "GE State (for binding tiara to hat)")
}
