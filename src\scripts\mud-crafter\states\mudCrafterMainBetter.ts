import { State, createState } from '../../../api/core/script/state'
import { Bank, Withdraw } from '../../../api/game/bank'
import { Dialogue } from '../../../api/game/dialogue'
import { Equipment } from '../../../api/game/equipment'
import { GameObjects } from '../../../api/game/gameObjects'
import { GameTab } from '../../../api/game/gameTab'
import { Inventory } from '../../../api/game/inventory'
import { Magic, SpellbookTypes } from '../../../api/game/magic'
import { Skill } from '../../../api/game/skill'
import { Varps } from '../../../api/game/varps'
import { WalkUtils, Walking } from '../../../api/game/walking'
import { Widgets } from '../../../api/game/widgets'
import { WorldHopping } from '../../../api/game/worldHopping'
import { Item } from '../../../api/model/item'
import { ItemContainer } from '../../../api/model/itemContainer'
import { Tile } from '../../../api/model/tile'
import { Time } from '../../../api/utils/time'
import { Player } from '../../../api/wrappers/player'
import { ItemId } from '../../../data/itemId'
import { Teleport } from '../../../data/teleport'
import { MudCrafter } from '../mudCrafter'
import { BindTiaraToHat } from './bindTiaraToHatState'
import { MudCrafterGeBetter } from './mudCrafterGeBetter'
import { SwitchSpellbook } from './switchSpellbook'

export class MudCrafterMainBetter extends State {
    filledSmallPouch: boolean = false

    onGameMessage(username: string, message: string): void {
        if(message.includes("powerful force take")) {
             MudCrafter.tripsDone++
        }
    }

    onAction(): void {
        if (!WorldHopping.switchToP2pExcept()) {
            return
        }

        if(true) {
            this.setState(new BindTiaraToHat( () => this, () => MudCrafter.resupply))
            return
        }
        
        if(Magic.currentSpellbook != SpellbookTypes.LUNAR_SPELLBOOK) {
            this.setState(new SwitchSpellbook(SpellbookTypes.LUNAR_SPELLBOOK, () => this, () => MudCrafter.resupply))
            return
        }

        this.setState(this.craftRunes)
    }

    getAndEquip(predicate: (item: Item) => boolean) {
        if (Equipment.isEquippedByPredicate(predicate)) {
            return true
        }
        if (!Bank.openNearest()) {
            return
        }
        if (!Withdraw.builder().predicate(predicate).amount(1).minimumAmount(1).ensureSpace().orState(MudCrafterGeBetter).withdraw()) {
            return
        }

        Inventory.get(Inventory.bank).getByPredicate(predicate)?.click(1007, 9)
        Time.sleepCycles(1)
    }

    get essenceInPouch() {
        return Varps.getVarbit(13682)
    }

    get isPouchFull() {
        return this.essenceInPouch >= this.maxPouchCapacity
    }
    get maxPouchCapacity() {
        if(Skill.RUNECRAFTING.getCurrentLevel() >= 85) {
            return 40
        }

        if(Skill.RUNECRAFTING.getCurrentLevel() >= 75) {
            return 27
        }

        if(Skill.RUNECRAFTING.getCurrentLevel() >= 50) {
            return 16
        }

          if(Skill.RUNECRAFTING.getCurrentLevel() >= 25) {
            return 8
        }
    }

    doBankingState = createState('Banking', () => {
        Walking.setRunAuto()

        if (Player.getRunEnergy() < 20) {
            this.setState(this.restore)
            return
        }

        if (!Bank.openNearest()) {
            return
        }



        Bank.depositAllByPredicate((i) => i.id == ItemId.MUD_RUNE)


         if(ItemContainer.allCombined().contains(ItemId.COLOSSAL_POUCH_26786)) {
            this.setState(this.fixPouch)
            return
        }


        if (!this.getAndEquip((i) => i.id == ItemId.BINDING_NECKLACE)) return
        if (!this.getAndEquip(Teleport.ringOfDuelingPredicate)) return

        if (!Withdraw.id(ItemId.RING_OF_THE_ELEMENTS_26818, 1).minimumAmount(1).orState(MudCrafterGeBetter).ensureSpace().withdraw()) {
            return
        }
        if (!Withdraw.id(ItemId.EARTH_TALISMAN).amount(2).minimumAmount(2).orState(MudCrafterGeBetter).ensureSpace().withdraw()) {
            return
        }
        if (!Withdraw.id(ItemId.EARTH_RUNE).amount(5000).minimumAmount(50).orState(MudCrafterGeBetter).ensureSpace().withdraw()) {
            return
        }
        if (!Withdraw.id(ItemId.PURE_ESSENCE).amount(23).minimumAmount(23).withdrawAll().orState(MudCrafterGeBetter).ensureSpace().withdraw()) {
            return
        }

        if (!Withdraw.predicate(i => i.definition.name.toLowerCase().includes("colossal pouch")).amount(1).minimumAmount(1).orState(null).ensureSpace().withdraw()) { //TODO: get colosal pouch state
            return
        }

       

        if(Inventory.contains(ItemId.PURE_ESSENCE) && !this.isPouchFull) {
            Inventory.get(Inventory.bank).byId(ItemId.COLOSSAL_POUCH)?.click( 1007, 9)
            Time.sleepCycles(2)
            return
        }

        this.setState(this.craftRunes)
    })

   

    fixPouch = createState('Fixing colossal pouch', () => {

      

        if(Inventory.contains(26784) ) {
            this.setDefaultState()
            return
        }

        if(Dialogue.isOpen()) {
            if(!Inventory.containsByPredicate(i => i.definition.name.toLowerCase().includes("colossal pouch"))){
                this.setDefaultState()
                return
            }
            Dialogue.goNext("Can you repair")
            return
        }

        if(!Withdraw.all(null, 
             Withdraw.id(ItemId.COLOSSAL_POUCH_26786).amount(1).minimumAmount(1).orState(() => this.doBankingState).ensureSpace(),
             Withdraw.id(ItemId.ASTRAL_RUNE).amount(1).minimumAmount(1).orState(MudCrafterGeBetter).ensureSpace(),
             Withdraw.id(ItemId.COSMIC_RUNE).amount(1).minimumAmount(1).orState(MudCrafterGeBetter).ensureSpace(),
             Withdraw.id(ItemId.AIR_RUNE).amount(2).minimumAmount(2).orState(MudCrafterGeBetter).ensureSpace(),
            )
            ) {
                return
             }

             if(Bank.isOpen()) {
                 Widgets.closeTopInterface()
             }

             if(!GameTab.magic.open()) {
                return
             }

             Widgets.get(********)?.click(57, 1)
             Time.sleepCycles(1)
             Widgets.get(4915214)?.click(57, 1)
             Time.sleep(() => Dialogue.isOpen())

    })

    craftRunes = createState('Crafting runes', () => {

        if(!Inventory.contains(ItemId.PURE_ESSENCE) && this.essenceInPouch > 0 && Inventory.contains(ItemId.EARTH_TALISMAN)) {
            Inventory.getById(ItemId.COLOSSAL_POUCH)?.click(57, 2)
            Time.sleep(() => Inventory.contains(ItemId.PURE_ESSENCE))
            return
        }


        if (
            !Inventory.contains(ItemId.PURE_ESSENCE) ||
            !Inventory.contains(ItemId.EARTH_TALISMAN) ||
            !Inventory.contains(ItemId.RING_OF_THE_ELEMENTS_26818) ||
            !Inventory.contains(ItemId.EARTH_RUNE)
        ) {
            this.setState(this.doBankingState)
            return
        }

        if (new Tile(2725, 4832, 0).distance() < 20) {
            Inventory.getById(ItemId.EARTH_TALISMAN)?.click(25, 0)
            Time.sleep(100, 300)
            GameObjects.getById(34762)?.click(2)
            Time.sleep(() => !Inventory.contains(ItemId.PURE_ESSENCE))
            return
        }

        if (new Tile(3183, 3166, 0).distance() > 30) {
            Teleport.ringOfTheElementsWater.process()
            return
        } else {
            const obj = GameObjects.getNearest(new Tile(3183, 3166, 0), (o) => o.id == 34815, 10)
            if (obj == null && !Walking.walkTo(new Tile(3183, 3166, 0), 6)) return

            obj?.click(3)
            Time.sleep(() => new Tile(2725, 4832, 0).distance() < 30)
        }
    })

    wearRingOfDueling = createState('Wearing ring of dueling', () => {
        if (Equipment.isEquippedByPredicate(Teleport.ringOfDuelingPredicate)) {
            this.setDefaultState()
            return
        }
        if (!Bank.openNearest()) {
            return
        }

        if (!Withdraw.builder().predicate(Teleport.ringOfDuelingPredicate).amount(1).minimumAmount(1).orState(MudCrafterGeBetter).ensureSpace().withdraw()) {
            return
        }

        Inventory.get(Inventory.bank).getByPredicate(Teleport.ringOfDuelingPredicate)?.click(1007, 9)
    })

    restore = createState('Restore from pool', () => {
        if (!WalkUtils.walkToFerox(new MudCrafterGeBetter())) {
            return
        }

        if (Player.getRunEnergy() > 80) {
            this.setDefaultState()
            return
        }

        GameObjects.getById(39651)?.click(3)

        if (Time.sleep(() => Walking.getRunEnergy() > 90)) {
            Time.sleep(2000, 2400)
        }
    })
}
